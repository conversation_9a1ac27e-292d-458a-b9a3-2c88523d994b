package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.dto.FileInfo;
import com.sdesrd.filetransfer.server.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.server.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.server.dto.FileUploadCompleteResponse;
import com.sdesrd.filetransfer.server.dto.TransferProgressResponse;
import com.sdesrd.filetransfer.server.entity.FileChunkRecord;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;
import com.sdesrd.filetransfer.server.mapper.FileChunkRecordMapper;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.FilePermissionUtils;
import com.sdesrd.filetransfer.server.util.PerformanceMonitor;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;
import com.sdesrd.filetransfer.server.util.UlidUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输服务
 */
@Slf4j
@Service
public class FileTransferService {
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;
    
    @Autowired
    private FileChunkRecordMapper chunkRecordMapper;
    
    @Autowired
    private FileTransferProperties properties;
    
    @Autowired
    private AuthService authService;
    
    /**
     * 初始化文件上传
     * 重构后的版本：使用ULID作为fileId，支持新的存储结构和增强的秒传机制
     */
    @Transactional
    public FileUploadInitResponse initUpload(FileUploadInitRequest request, String username, String clientIp) {
        // 获取用户配置
        UserConfig userConfig = authService.getUserConfig(username);

        // 验证文件大小
        if (request.getFileSize() > userConfig.getMaxFileSize()) {
            throw new FileTransferException("文件大小超出限制: " + request.getFileSize() + " > " + userConfig.getMaxFileSize());
        }

        // 验证文件MD5
        if (!StringUtils.hasText(request.getFileMd5())) {
            throw new FileTransferException("文件MD5不能为空");
        }

        // 验证文件后缀名（允许为空）
        String fileExtension = request.getFileExtension();
        if (fileExtension == null) {
            fileExtension = "";
        }

        // 生成传输ID和文件ID
        String transferId = UUID.randomUUID().toString();
        String fileId = UlidUtils.generateUlid(); // 使用ULID作为fileId
        String fileMd5 = request.getFileMd5();
        String currentTime = Instant.now().toString();
        
        // 计算分块信息
        long chunkSize = request.getChunkSize() != null ? request.getChunkSize() : userConfig.getDefaultChunkSize();
        int totalChunks = (int) Math.ceil((double) request.getFileSize() / chunkSize);

        // 构建最终存储的文件名：md5.{后缀名} 或 md5（无后缀时）
        String finalFileName = StringUtils.hasText(fileExtension) ?
            fileMd5 + "." + fileExtension : fileMd5;

        // 构建文件路径：新的存储结构 YYYYMM/fileId/md5.{后缀名}
        String filePath = buildNewFilePath(fileId, finalFileName);

        // 增强的秒传检查：同时匹配fileId和md5
        boolean fastUpload = false;
        FileTransferRecord existingRecord = null;

        // 首先检查是否存在相同fileId和md5的记录
        existingRecord = findCompletedFileByFileIdAndMd5(fileId, fileMd5, username);
        if (existingRecord != null && verifyExistingFile(existingRecord, fileMd5)) {
            log.info("文件秒传（fileId+MD5匹配） - 用户: {}, fileId: {}, MD5: {}", username, fileId, fileMd5);
            fastUpload = true;
        } else {
            // 如果没有完全匹配的记录，检查是否存在相同MD5的文件
            existingRecord = findAnyCompletedFileByMd5(fileMd5, username);
            if (existingRecord != null && verifyExistingFile(existingRecord, fileMd5)) {
                log.info("文件秒传（MD5匹配） - 用户: {}, 源文件: {}, MD5: {}", username, existingRecord.getFilePath(), fileMd5);
                fastUpload = true;
            }
        }

        // 创建传输记录
        FileTransferRecord record = new FileTransferRecord();
        record.setId(transferId);
        record.setFileId(fileId); // 使用ULID作为fileId
        record.setFileName(finalFileName); // 使用最终的文件名
        record.setFileSize(request.getFileSize());
        record.setFilePath(filePath);
        record.setTransferredSize(fastUpload ? request.getFileSize() : 0L);
        record.setClientIp(clientIp);
        record.setCreateTime(currentTime);
        record.setUpdateTime(currentTime);
        record.setTotalChunks(totalChunks);
        record.setCompletedChunks(fastUpload ? totalChunks : 0);
        // 在extInfo中存储原始MD5值和文件后缀名
        record.setExtInfo("{\"originalMd5\":\"" + fileMd5 + "\",\"fileExtension\":\"" + fileExtension + "\"}");
        
        if (fastUpload) {
            // 秒传：复制已存在的文件到新位置
            try {
                // 确保目标目录存在
                ensureDirectoryExists(filePath);

                // 复制文件
                FileUtils.copyFile(existingRecord.getFilePath(), filePath);

                // 设置文件为只读权限
                FilePermissionUtils.setReadOnly(filePath);

                record.setStatus(2); // 传输完成
                record.setCompleteTime(currentTime);
                record.setTransferredSize(request.getFileSize());
                record.setCompletedChunks(totalChunks);

                log.info("秒传复制文件成功 - 源: {}, 目标: {}, MD5: {}",
                    existingRecord.getFilePath(), filePath, fileMd5);

            } catch (Exception e) {
                log.warn("秒传复制文件失败，降级为普通上传 - 错误: {}", e.getMessage());
                fastUpload = false;
                record.setStatus(1); // 传输中
                record.setTransferredSize(0L);
                record.setCompletedChunks(0);
                ensureDirectoryExists(filePath);
            }
        } else {
            record.setStatus(1); // 传输中
            ensureDirectoryExists(filePath);
        }
        
        transferRecordMapper.insert(record);

        FileUploadInitResponse response = new FileUploadInitResponse();
        response.setTransferId(transferId);
        response.setFileId(fileId); // 返回ULID格式的fileId
        response.setFileName(finalFileName); // 返回最终存储的文件名
        response.setChunkSize(chunkSize);
        response.setTotalChunks(totalChunks);
        response.setFastUpload(fastUpload);

        log.info("初始化上传完成 - 用户: {}, 传输ID: {}, fileId: {}, 文件名: {}, 秒传: {}",
            username, transferId, fileId, finalFileName, fastUpload);
        return response;
    }
    
    /**
     * 上传文件分块
     */
    @Transactional
    public void uploadChunk(String transferId, Integer chunkIndex, String chunkMd5, MultipartFile chunkFile, String username) {
        // 查询传输记录
        FileTransferRecord record = transferRecordMapper.selectById(transferId);
        if (record == null) {
            throw new FileTransferException("传输记录不存在: " + transferId);
        }
        
        if (record.getStatus() == 2) {
            log.warn("传输已完成，忽略分块上传 - 传输ID: {}, 分块: {}", transferId, chunkIndex);
            return;
        }
        
        if (record.getStatus() == 3) {
            throw new FileTransferException("传输已失败，无法继续上传 - 传输ID: " + transferId);
        }
        
        // 获取用户配置
        UserConfig userConfig = authService.getUserConfig(username);
        
        // 检查分块是否已存在
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("chunk_index", chunkIndex);
        FileChunkRecord existingChunk = chunkRecordMapper.selectOne(chunkQuery);
        
        if (existingChunk != null && existingChunk.getStatus() == 1) {
            log.debug("分块已存在，跳过上传 - 传输ID: {}, 分块: {}", transferId, chunkIndex);
            return;
        }
        
        try {
            // 验证分块大小
            if (chunkFile.getSize() > userConfig.getMaxInMemorySize()) {
                throw new FileTransferException("分块大小超出限制: " + chunkFile.getSize() + " > " + userConfig.getMaxInMemorySize());
            }
            
            // 应用上传限速
            if (userConfig.getRateLimitEnabled()) {
                RateLimitUtils.applyRateLimit(username + "_upload", userConfig.getUploadRateLimit(), chunkFile.getSize());
            }
            
            // 验证分块MD5
            byte[] chunkData = chunkFile.getBytes();
            String actualMd5 = FileUtils.calculateMD5(chunkData);
            if (!actualMd5.equals(chunkMd5)) {
                throw new FileTransferException("分块MD5校验失败: expected=" + chunkMd5 + ", actual=" + actualMd5);
            }
            
            // 保存分块文件
            String chunkPath = buildChunkPath(record.getFilePath(), chunkIndex);
            ensureDirectoryExists(chunkPath);
            
            try (FileOutputStream fos = new FileOutputStream(chunkPath)) {
                fos.write(chunkData);
                fos.flush();
            }
            
            // 记录分块信息
            FileChunkRecord chunkRecord = new FileChunkRecord();
            chunkRecord.setId(UUID.randomUUID().toString());
            chunkRecord.setTransferId(transferId);
            chunkRecord.setFileId(record.getFileId());
            chunkRecord.setChunkIndex(chunkIndex);
            chunkRecord.setChunkSize((long) chunkData.length);
            chunkRecord.setChunkOffset((long) chunkIndex * userConfig.getDefaultChunkSize());
            chunkRecord.setChunkPath(chunkPath);
            chunkRecord.setChunkMd5(chunkMd5);
            chunkRecord.setStatus(1); // 完成
            chunkRecord.setRetryCount(0);
            chunkRecord.setCreateTime(Instant.now().toString());
            chunkRecord.setCompleteTime(Instant.now().toString());
            
            if (existingChunk != null) {
                chunkRecord.setId(existingChunk.getId());
                chunkRecordMapper.updateById(chunkRecord);
            } else {
                chunkRecordMapper.insert(chunkRecord);
            }
            
            // 更新传输记录
            updateTransferProgress(transferId);
            
            log.debug("分块上传完成 - 传输ID: {}, 分块: {}, 大小: {}", transferId, chunkIndex, chunkData.length);
            
        } catch (IOException e) {
            log.error("保存分块文件失败 - 传输ID: {}, 分块: {}", transferId, chunkIndex, e);
            
            // 记录失败的分块
            if (existingChunk == null) {
                FileChunkRecord failedChunk = new FileChunkRecord();
                failedChunk.setId(UUID.randomUUID().toString());
                failedChunk.setTransferId(transferId);
                failedChunk.setFileId(record.getFileId());
                failedChunk.setChunkIndex(chunkIndex);
                failedChunk.setStatus(2); // 失败
                failedChunk.setFailReason("保存分块文件失败: " + e.getMessage());
                failedChunk.setCreateTime(Instant.now().toString());
                chunkRecordMapper.insert(failedChunk);
            }
            
            throw new FileTransferException("保存分块文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成文件上传
     * 重构后的版本：返回完整的上传结果信息，包括fileId和相对路径
     */
    @Transactional
    public FileUploadCompleteResponse completeUpload(String transferId, String username) {
        // 查询传输记录
        FileTransferRecord record = transferRecordMapper.selectById(transferId);
        if (record == null) {
            throw new FileTransferException("传输记录不存在: " + transferId);
        }
        
        if (record.getStatus() == 2) {
            log.info("传输已完成 - 传输ID: {}", transferId);
            // 构建并返回完成响应
            return buildCompleteResponse(record, transferId, username, true);
        }
        
        // 检查所有分块是否上传完成
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("status", 1);
        List<FileChunkRecord> completedChunks = chunkRecordMapper.selectList(chunkQuery);
        
        if (completedChunks.size() != record.getTotalChunks()) {
            throw new FileTransferException("文件分块未完全上传: " + completedChunks.size() + "/" + record.getTotalChunks());
        }
        
        try {
            // 合并分块文件
            mergeChunks(record, completedChunks);
            
            // 验证合并后的文件
            File mergedFile = new File(record.getFilePath());
            if (!mergedFile.exists()) {
                throw new IOException("合并后的文件不存在: " + record.getFilePath());
            }
            
            if (mergedFile.length() != record.getFileSize()) {
                throw new IOException("合并文件大小不匹配: expected=" + record.getFileSize() + ", actual=" + mergedFile.length());
            }
            
            // 验证文件MD5（使用extInfo中的原始MD5）
            String originalMd5 = extractOriginalMd5FromExtInfo(record.getExtInfo());
            if (StringUtils.hasText(originalMd5)) {
                String actualMd5 = FileUtils.calculateFileMD5(mergedFile);
                if (!actualMd5.equals(originalMd5)) {
                    log.warn("文件MD5不匹配，但继续完成传输 - 传输ID: {}, expected: {}, actual: {}",
                            transferId, originalMd5, actualMd5);
                }
            }

            // 设置文件为只读权限
            FilePermissionUtils.setReadOnly(mergedFile);

            // 更新传输记录状态
            record.setStatus(2); // 传输完成
            record.setTransferredSize(record.getFileSize());
            record.setCompletedChunks(record.getTotalChunks());
            record.setCompleteTime(Instant.now().toString());
            record.setUpdateTime(Instant.now().toString());

            transferRecordMapper.updateById(record);

            // 记录上传统计
            PerformanceMonitor.recordUpload(record.getFileSize(), true);

            log.info("文件上传完成 - 传输ID: {}, 文件: {}, 大小: {}", transferId, record.getFileName(), record.getFileSize());

            // 构建并返回完成响应
            return buildCompleteResponse(record, transferId, username, false);
            
        } catch (Exception e) {
            log.error("完成上传失败 - 传输ID: {}", transferId, e);
            
            // 更新为失败状态
            record.setStatus(3); // 传输失败
            record.setFailReason("完成上传失败: " + e.getMessage());
            record.setUpdateTime(Instant.now().toString());
            transferRecordMapper.updateById(record);

            // 记录上传失败统计
            PerformanceMonitor.recordUpload(record.getFileSize(), false);

            throw new FileTransferException("完成上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载文件
     */
    public void downloadFile(String fileId, String username, HttpServletResponse response) {
        // 使用带容错机制的查找方法
        FileTransferRecord record = findCompletedFileByMd5WithFallback(fileId, username);
        if (record == null) {
            throw new FileTransferException("文件不存在或无权限访问: " + fileId);
        }
        
        File file = new File(record.getFilePath());
        if (!file.exists()) {
            throw new FileTransferException("文件不存在: " + record.getFilePath());
        }
        
        try {
            // 获取用户配置
            UserConfig userConfig = authService.getUserConfig(username);
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setContentLengthLong(file.length());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeFileName(record.getFileName()) + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            // 应用下载限速并传输文件
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalRead = 0;
                long startTime = System.currentTimeMillis();
                
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                    totalRead += bytesRead;
                    
                    // 应用下载限速
                    if (userConfig.getRateLimitEnabled() && userConfig.getDownloadRateLimit() > 0) {
                        RateLimitUtils.applyRateLimit(username + "_download", userConfig.getDownloadRateLimit(), bytesRead);
                    }
                    
                    // 每传输1MB输出一次日志
                    if (totalRead % (1024 * 1024) == 0) {
                        log.debug("下载进度 - 用户: {}, 文件: {}, 已传输: {}/{}", 
                                username, record.getFileName(), totalRead, file.length());
                    }
                }
                
                os.flush();
                
                long duration = System.currentTimeMillis() - startTime;
                double speed = duration > 0 ? (double) totalRead / duration * 1000 : 0; // bytes/s
                
                // 记录下载统计
                PerformanceMonitor.recordDownload(totalRead, true);

                log.info("文件下载完成 - 用户: {}, 文件: {}, 大小: {}, 耗时: {}ms, 速度: {}/s",
                        username, record.getFileName(), totalRead, duration, FileUtils.formatFileSize((long) speed));
            }
            
        } catch (IOException e) {
            log.error("下载文件失败 - 文件ID: {}", fileId, e);

            // 记录下载失败统计
            PerformanceMonitor.recordDownload(file.length(), false);

            throw new FileTransferException("下载文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询传输进度
     */
    public TransferProgressResponse queryProgress(String transferId, String username) {
        FileTransferRecord record = transferRecordMapper.selectById(transferId);
        if (record == null) {
            throw new FileTransferException("传输记录不存在: " + transferId);
        }
        
        // 实时查询已完成的分块数
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("status", 1);
        int completedChunks = chunkRecordMapper.selectCount(chunkQuery).intValue();
        
        // 计算实际进度
        double progress = record.getTotalChunks() > 0 ? 
            (double) completedChunks / record.getTotalChunks() * 100 : 0;
        
        long transferredSize = record.getTotalChunks() > 0 ? 
            (long) ((double) completedChunks / record.getTotalChunks() * record.getFileSize()) : 0;
        
        if (record.getStatus() == 2) {
            transferredSize = record.getFileSize();
            progress = 100.0;
            completedChunks = record.getTotalChunks();
        }
        
        TransferProgressResponse response = new TransferProgressResponse();
        response.setTransferId(transferId);
        response.setFileName(record.getFileName());
        response.setTotalSize(record.getFileSize());
        response.setTransferredSize(transferredSize);
        response.setProgress(progress);
        response.setTotalChunks(record.getTotalChunks());
        response.setCompletedChunks(completedChunks);
        response.setCompleted(record.getStatus() == 2);
        response.setStatus(record.getStatus());
        
        return response;
    }
    
    /**
     * 查找已完成的文件记录（按fileId和MD5匹配）
     * 增强的秒传机制：同时匹配fileId和MD5
     */
    private FileTransferRecord findCompletedFileByFileIdAndMd5(String fileId, String fileMd5, String username) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("file_id", fileId).eq("status", 2);

            List<FileTransferRecord> records = transferRecordMapper.selectList(query);

            // 进一步验证MD5匹配
            for (FileTransferRecord record : records) {
                if (record.getExtInfo() != null && record.getExtInfo().contains(fileMd5)) {
                    return record;
                }
            }

            return null;
        } catch (Exception e) {
            log.warn("按fileId和MD5查询失败 - fileId: {}, MD5: {}, 错误: {}", fileId, fileMd5, e.getMessage());
            return null;
        }
    }

    /**
     * 查找任意已完成的文件记录（按MD5）
     * 用于MD5匹配的秒传
     */
    private FileTransferRecord findAnyCompletedFileByMd5(String fileMd5, String username) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("status", 2);
            query.like("ext_info", fileMd5); // 在扩展信息中查找MD5

            List<FileTransferRecord> records = transferRecordMapper.selectList(query);

            return records.stream()
                .filter(record -> record.getFilePath() != null)
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("按MD5查询失败 - MD5: {}, 错误: {}", fileMd5, e.getMessage());
            return null;
        }
    }

    /**
     * 验证现有文件的完整性
     * 重新计算文件MD5并与预期值比较
     */
    private boolean verifyExistingFile(FileTransferRecord record, String expectedMd5) {
        if (record == null || record.getFilePath() == null) {
            return false;
        }

        File file = new File(record.getFilePath());
        if (!file.exists() || !file.isFile()) {
            log.warn("文件不存在，无法验证: {}", record.getFilePath());
            return false;
        }

        try {
            String actualMd5 = FileUtils.calculateFileMD5(file);
            boolean matches = expectedMd5.equals(actualMd5);

            if (!matches) {
                log.warn("文件MD5不匹配，可能已被外部修改 - 文件: {}, 预期: {}, 实际: {}",
                    record.getFilePath(), expectedMd5, actualMd5);

                // 文件已损坏，删除整个文件夹
                try (FilePermissionUtils.TemporaryPermissionManager permManager =
                        new FilePermissionUtils.TemporaryPermissionManager(file.getParentFile())) {

                    permManager.makeWritable();
                    deleteFileAndDirectory(file);
                    log.info("已删除损坏的文件及其目录: {}", record.getFilePath());

                } catch (Exception e) {
                    log.error("删除损坏文件失败: {}", record.getFilePath(), e);
                }
            }

            return matches;

        } catch (Exception e) {
            log.error("验证文件MD5失败: {}", record.getFilePath(), e);
            return false;
        }
    }

    /**
     * 删除文件及其父目录（如果为空）
     */
    private void deleteFileAndDirectory(File file) throws IOException {
        if (file.exists()) {
            file.delete();
        }

        // 尝试删除父目录（如果为空）
        File parentDir = file.getParentFile();
        if (parentDir != null && parentDir.exists()) {
            String[] children = parentDir.list();
            if (children == null || children.length == 0) {
                parentDir.delete();
                log.debug("删除空目录: {}", parentDir.getAbsolutePath());

                // 递归删除上级空目录
                File grandParent = parentDir.getParentFile();
                if (grandParent != null && grandParent.exists()) {
                    String[] grandChildren = grandParent.list();
                    if (grandChildren == null || grandChildren.length == 0) {
                        grandParent.delete();
                        log.debug("删除空目录: {}", grandParent.getAbsolutePath());
                    }
                }
            }
        }
    }

    /**
     * 查找已完成的文件记录（按MD5和用户）
     */
    private FileTransferRecord findCompletedFileByMd5(String fileMd5, String username) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("file_id", fileMd5).eq("status", 2);

            List<FileTransferRecord> records = transferRecordMapper.selectList(query);

            // 使用新的文件路径规则，不再按用户过滤
            return records.stream()
                .filter(record -> record.getFilePath() != null)
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("数据库查询失败，将尝试通过文件系统查找 - fileId: {}, 错误: {}", fileMd5, e.getMessage());
            return null;
        }
    }
    
    /**
     * 带容错机制的文件记录查找
     * 如果数据库查询失败或没有记录，尝试通过文件路径规则查找物理文件
     */
    private FileTransferRecord findCompletedFileByMd5WithFallback(String fileId, String username) {
        // 首先尝试从数据库查找
        FileTransferRecord record = findCompletedFileByMd5(fileId, username);
        
        if (record != null && checkFileExists(record.getFilePath())) {
            return record;
        }
        
        // 数据库查不到或文件不存在，尝试通过文件路径规则查找
        log.info("数据库查询无结果或文件不存在，尝试通过文件路径规则查找 - fileId: {}", fileId);
        String filePath = tryFindFileByPathRule(fileId);
        
        if (filePath != null) {
            // 构建一个虚拟的记录对象用于返回
            File file = new File(filePath);
            FileTransferRecord virtualRecord = new FileTransferRecord();
            virtualRecord.setFileId(fileId);
            virtualRecord.setFileName(file.getName());
            virtualRecord.setFileSize(file.length());
            virtualRecord.setFilePath(filePath);
            virtualRecord.setStatus(2); // 标记为已完成
            virtualRecord.setCompleteTime(Instant.ofEpochMilli(file.lastModified()).toString());
            
            log.info("通过文件路径规则找到文件 - fileId: {}, 路径: {}", fileId, filePath);
            return virtualRecord;
        }
        
        return null;
    }
    
    /**
     * 根据文件路径规则尝试查找物理文件
     * 支持新旧两种存储规则：
     * 1. 新规则：${storage-path}/YYYYMM/fileId（ULID）/md5.{后缀名}
     * 2. 旧规则：${storage-path}/fileId前4位/fileId/
     */
    private String tryFindFileByPathRule(String fileId) {
        // 首先尝试新的ULID存储规则
        String filePath = tryFindFileByNewPathRule(fileId);
        if (filePath != null) {
            return filePath;
        }

        // 如果新规则找不到，尝试旧的MD5存储规则
        return tryFindFileByOldPathRule(fileId);
    }

    /**
     * 根据新的ULID存储规则查找文件
     * 新规则：${storage-path}/YYYYMM/fileId（ULID）/md5.{后缀名}
     */
    private String tryFindFileByNewPathRule(String fileId) {
        try {
            String basePath = properties.getDefaultConfig().getStoragePath();

            // 如果fileId是ULID格式，从中提取年月信息
            if (UlidUtils.isValidUlid(fileId)) {
                String yearMonth = UlidUtils.extractYearMonth(fileId);
                if (yearMonth != null) {
                    Path dirPath = Paths.get(basePath, yearMonth, fileId);

                    if (Files.exists(dirPath) && Files.isDirectory(dirPath)) {
                        // 查找目录下的文件
                        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dirPath)) {
                            for (Path path : stream) {
                                if (Files.isRegularFile(path)) {
                                    log.info("通过新存储规则找到文件 - fileId: {}, 路径: {}", fileId, path);
                                    return path.toString();
                                }
                            }
                        }
                    }
                }
            }

            // 如果不是ULID格式，尝试扫描所有年月目录
            try (DirectoryStream<Path> yearMonthStream = Files.newDirectoryStream(Paths.get(basePath))) {
                for (Path yearMonthPath : yearMonthStream) {
                    if (Files.isDirectory(yearMonthPath)) {
                        Path fileIdPath = yearMonthPath.resolve(fileId);
                        if (Files.exists(fileIdPath) && Files.isDirectory(fileIdPath)) {
                            try (DirectoryStream<Path> fileStream = Files.newDirectoryStream(fileIdPath)) {
                                for (Path filePath : fileStream) {
                                    if (Files.isRegularFile(filePath)) {
                                        log.info("通过新存储规则扫描找到文件 - fileId: {}, 路径: {}", fileId, filePath);
                                        return filePath.toString();
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("通过新存储规则查找文件失败 - fileId: {}, 错误: {}", fileId, e.getMessage());
        }

        return null;
    }

    /**
     * 根据旧的MD5存储规则查找文件
     * 旧规则：${storage-path}/fileId前4位/fileId/
     */
    private String tryFindFileByOldPathRule(String fileId) {
        try {
            String basePath = properties.getDefaultConfig().getStoragePath();
            String prefix = fileId.substring(0, Math.min(4, fileId.length()));

            // 构建目录路径
            Path dirPath = Paths.get(basePath, prefix, fileId);

            if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
                return null;
            }

            // 查找目录下的文件
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(dirPath)) {
                for (Path path : stream) {
                    if (Files.isRegularFile(path)) {
                        // 验证文件的MD5是否匹配
                        String actualMd5 = FileUtils.calculateFileMD5(path.toFile());
                        if (fileId.equals(actualMd5)) {
                            log.info("通过旧存储规则找到匹配的文件 - fileId: {}, 路径: {}", fileId, path);
                            return path.toString();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("通过旧存储规则查找文件失败 - fileId: {}, 错误: {}", fileId, e.getMessage());
        }

        return null;
    }
    
    /**
     * 重建SQLite数据库
     * 通过扫描存储目录下的文件，重建数据库记录
     * 
     * @return 重建统计信息
     */
    @Transactional
    public Map<String, Object> rebuildDatabase() {
        log.info("开始重建数据库...");
        
        // 备份当前数据库
        String backupPath = backupCurrentDatabase();
        
        Map<String, Object> result = new HashMap<>();
        result.put("backupPath", backupPath);
        
        try {
            String basePath = properties.getDefaultConfig().getStoragePath();
            File baseDir = new File(basePath);
            
            if (!baseDir.exists() || !baseDir.isDirectory()) {
                throw new FileTransferException("存储目录不存在: " + basePath);
            }
            
            int scannedFiles = 0;
            int rebuiltRecords = 0;
            int skippedFiles = 0;
            
            log.info("开始扫描存储目录: {}", basePath);
            
            // 遍历所有子目录（前4位目录）
            for (File prefixDir : baseDir.listFiles()) {
                if (!prefixDir.isDirectory()) {
                    continue;
                }
                
                // 遍历fileId目录
                for (File fileIdDir : prefixDir.listFiles()) {
                    if (!fileIdDir.isDirectory()) {
                        continue;
                    }
                    
                    String dirName = fileIdDir.getName();
                    // 验证目录名是否为有效的fileId（32位MD5）
                    if (dirName.length() != 32 || !dirName.matches("[0-9a-fA-F]+")) {
                        log.debug("跳过无效的目录名: {}", dirName);
                        continue;
                    }
                    
                    // 查找目录下的文件
                    File[] files = fileIdDir.listFiles((file) -> file.isFile() && !file.getName().endsWith(".chunk"));
                    if (files == null || files.length == 0) {
                        log.debug("目录下没有找到有效文件: {}", fileIdDir.getPath());
                        continue;
                    }
                    
                    for (File file : files) {
                        scannedFiles++;
                        
                        try {
                            // 验证文件MD5是否与目录名匹配
                            String actualMd5 = FileUtils.calculateFileMD5(file);
                            if (!actualMd5.equalsIgnoreCase(dirName)) {
                                log.warn("文件MD5不匹配，跳过 - 文件: {}, 期望: {}, 实际: {}", 
                                        file.getPath(), dirName, actualMd5);
                                skippedFiles++;
                                continue;
                            }
                            
                            // 检查数据库中是否已存在该文件记录
                            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
                            query.eq("file_id", actualMd5).eq("status", 2);
                            List<FileTransferRecord> existingRecords = transferRecordMapper.selectList(query);
                            
                            boolean hasValidRecord = existingRecords.stream()
                                    .anyMatch(record -> checkFileExists(record.getFilePath()));
                            
                            if (hasValidRecord) {
                                log.debug("文件记录已存在且有效，跳过 - fileId: {}", actualMd5);
                                continue;
                            }
                            
                            // 创建新的传输记录
                            FileTransferRecord record = new FileTransferRecord();
                            record.setId(UUID.randomUUID().toString());
                            record.setFileId(actualMd5);
                            record.setFileName(file.getName());
                            record.setFileSize(file.length());
                            record.setFilePath(file.getAbsolutePath());
                            record.setTransferredSize(file.length());
                            record.setTotalChunks(1);
                            record.setCompletedChunks(1);
                            record.setStatus(2); // 已完成
                            record.setClientIp("rebuilt-from-filesystem");
                            
                            String fileTime = Instant.ofEpochMilli(file.lastModified()).toString();
                            record.setCreateTime(fileTime);
                            record.setUpdateTime(fileTime);
                            record.setCompleteTime(fileTime);
                            
                            transferRecordMapper.insert(record);
                            rebuiltRecords++;
                            
                            log.info("重建文件记录 - fileId: {}, 文件: {}, 大小: {}", 
                                    actualMd5, file.getName(), file.length());
                            
                        } catch (Exception e) {
                            log.error("处理文件失败，跳过 - 文件: {}, 错误: {}", file.getPath(), e.getMessage());
                            skippedFiles++;
                        }
                    }
                }
            }
            
            result.put("success", true);
            result.put("scannedFiles", scannedFiles);
            result.put("rebuiltRecords", rebuiltRecords);
            result.put("skippedFiles", skippedFiles);
            result.put("message", String.format("数据库重建完成 - 扫描文件: %d, 重建记录: %d, 跳过文件: %d", 
                    scannedFiles, rebuiltRecords, skippedFiles));
            
            log.info("数据库重建完成 - 扫描: {}, 重建: {}, 跳过: {}", scannedFiles, rebuiltRecords, skippedFiles);
            
        } catch (Exception e) {
            log.error("重建数据库失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            throw new FileTransferException("重建数据库失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 备份当前数据库
     * 
     * @return 备份文件路径
     */
    private String backupCurrentDatabase() {
        try {
            String databasePath = properties.getDatabasePath();
            File databaseFile = new File(databasePath);
            
            if (!databaseFile.exists()) {
                log.info("数据库文件不存在，无需备份: {}", databasePath);
                return null;
            }
            
            String timestamp = Instant.now().toString().replaceAll("[:\\.]", "-");
            String backupPath = databasePath + ".backup." + timestamp;
            
            FileUtils.copyFile(databaseFile.getAbsolutePath(), backupPath);
            log.info("数据库备份完成 - 原文件: {}, 备份文件: {}", databasePath, backupPath);
            
            return backupPath;
            
        } catch (Exception e) {
            log.error("备份数据库失败", e);
            throw new FileTransferException("备份数据库失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新传输进度
     */
    private void updateTransferProgress(String transferId) {
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("status", 1);
        int completedChunks = chunkRecordMapper.selectCount(chunkQuery).intValue();
        
        FileTransferRecord record = new FileTransferRecord();
        record.setId(transferId);
        record.setCompletedChunks(completedChunks);
        record.setUpdateTime(Instant.now().toString());
        
        transferRecordMapper.updateById(record);
    }
    
    /**
     * 合并分块文件
     */
    private void mergeChunks(FileTransferRecord record, List<FileChunkRecord> chunks) throws IOException {
        // 按分块序号排序
        chunks.sort((a, b) -> a.getChunkIndex().compareTo(b.getChunkIndex()));
        
        try (FileOutputStream fos = new FileOutputStream(record.getFilePath())) {
            for (FileChunkRecord chunk : chunks) {
                File chunkFile = new File(chunk.getChunkPath());
                if (!chunkFile.exists()) {
                    throw new IOException("分块文件不存在: " + chunk.getChunkPath());
                }
                
                try (FileInputStream fis = new FileInputStream(chunkFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
                
                // 删除分块文件
                if (!chunkFile.delete()) {
                    log.warn("删除分块文件失败: {}", chunk.getChunkPath());
                }
            }
            
            fos.flush();
        }
        
        log.info("文件合并完成 - 传输ID: {}, 分块数: {}", record.getId(), chunks.size());
    }
    
    /**
     * 构建新的文件路径
     * 新的存储规则：${storage-path}/YYYYMM/fileId（ULID）/md5.{后缀名}
     */
    private String buildNewFilePath(String fileId, String fileName) {
        // 获取默认存储路径
        String basePath = properties.getDefaultConfig().getStoragePath();

        // 从ULID提取年月信息
        String yearMonth = UlidUtils.extractYearMonth(fileId);
        if (yearMonth == null) {
            log.warn("无法从fileId提取年月信息，使用当前年月: {}", fileId);
            yearMonth = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMM"));
        }

        // 构建路径: storage-path/YYYYMM/fileId/fileName
        String safeName = sanitizeFileName(fileName);
        return Paths.get(basePath, yearMonth, fileId, safeName).toString();
    }

    /**
     * 构建文件路径（兼容旧版本）
     * 旧的存储规则：${storage-path}/fileId前4位/fileId/实体文件名
     */
    private String buildFilePath(String fileId, String fileName) {
        // 获取默认存储路径（统一使用默认配置，不再按用户分离）
        String basePath = properties.getDefaultConfig().getStoragePath();

        // fileId前4位作为第一级目录
        String prefix = fileId.substring(0, Math.min(4, fileId.length()));

        // 构建路径: storage-path/前4位/fileId/实体文件名
        String safeName = sanitizeFileName(fileName);
        return Paths.get(basePath, prefix, fileId, safeName).toString();
    }
    
    /**
     * 构建分块文件路径
     */
    private String buildChunkPath(String filePath, int chunkIndex) {
        return filePath + ".chunk." + String.format("%06d", chunkIndex);
    }
    
    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unnamed";
        }
        // 移除或替换文件名中的非法字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }
    
    /**
     * 编码文件名用于HTTP响应
     */
    private String encodeFileName(String fileName) {
        try {
            return java.net.URLEncoder.encode(fileName, "UTF-8")
                    .replaceAll("\\+", "%20"); // 空格用%20而不是+
        } catch (Exception e) {
            return fileName;
        }
    }
    
    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String filePath) {
        Path parentDir = Paths.get(filePath).getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            try {
                Files.createDirectories(parentDir);
                log.debug("创建目录: {}", parentDir);
            } catch (IOException e) {
                throw new RuntimeException("创建目录失败: " + parentDir, e);
            }
        }
    }
    
    /**
     * 检查文件是否存在
     */
    private boolean checkFileExists(String filePath) {
        return filePath != null && Files.exists(Paths.get(filePath));
    }
    
    /**
     * 获取文件详细信息
     * 
     * @param fileId 文件ID（MD5值）
     * @param username 用户名
     * @return 文件信息
     */
    public FileInfo getFileInfo(String fileId, String username) {
        // 使用带容错机制的查找方法
        FileTransferRecord record = findCompletedFileByMd5WithFallback(fileId, username);
        if (record == null) {
            throw new FileTransferException("文件不存在或无权限访问: " + fileId);
        }
        
        // 检查物理文件是否存在
        if (!checkFileExists(record.getFilePath())) {
            throw new FileTransferException("文件物理存储不存在: " + fileId);
        }
        
        // 获取用户配置以计算相对路径
        UserConfig userConfig = authService.getUserConfig(username);
        String userStoragePath = userConfig.getStoragePath();
        
        // 构建文件信息
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileId(record.getFileId());
        fileInfo.setFileName(record.getFileName());
        fileInfo.setFileSize(record.getFileSize());
        fileInfo.setFileType(detectFileType(record.getFileName()));
        fileInfo.setUploadTime(record.getCompleteTime());
        
        // 计算相对路径（相对于用户存储根目录）
        String relativePath = calculateRelativePath(record.getFilePath(), userStoragePath);
        fileInfo.setRelativePath(relativePath);
        
        // 获取文件的额外信息
        File file = new File(record.getFilePath());
        if (file.exists()) {
            fileInfo.setLastModified(file.lastModified());
            fileInfo.setCanRead(file.canRead());
            fileInfo.setCanWrite(file.canWrite());
        }
        
        return fileInfo;
    }
    
    /**
     * 通过相对路径获取文件信息
     * 支持从相对路径获取文件信息，客户端可据此获取fileId进行下载
     * 
     * @param relativePath 相对路径
     * @param username 用户名
     * @return 文件信息，如果文件不存在则返回null
     */
    public FileInfo getFileInfoByPath(String relativePath, String username) {
        // 安全验证：确保相对路径不包含危险操作
        if (!validateRelativePath(relativePath)) {
            throw new FileTransferException("非法的文件路径: " + relativePath);
        }
        
        // 获取默认存储路径（统一使用默认配置）
        String basePath = properties.getDefaultConfig().getStoragePath();
        
        // 构建完整文件路径
        Path fullPath = Paths.get(basePath, relativePath).normalize();
        File file = fullPath.toFile();
        
        // 安全检查：确保文件在存储目录内
        if (!file.getAbsolutePath().startsWith(new File(basePath).getAbsolutePath())) {
            throw new FileTransferException("文件路径超出存储范围: " + relativePath);
        }
        
        // 检查文件是否存在
        if (!file.exists() || !file.isFile()) {
            log.debug("文件不存在: {}", relativePath);
            return null;
        }
        
        try {
            // 计算文件MD5作为fileId
            String fileId = FileUtils.calculateFileMD5(file);
            
            // 先尝试从数据库查找记录
            FileTransferRecord record = findCompletedFileByMd5WithFallback(fileId, username);
            
            // 构建文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileId(fileId);
            fileInfo.setFileName(file.getName());
            fileInfo.setFileSize(file.length());
            fileInfo.setFileType(detectFileType(file.getName()));
            fileInfo.setRelativePath(relativePath);
            fileInfo.setLastModified(file.lastModified());
            fileInfo.setCanRead(file.canRead());
            fileInfo.setCanWrite(file.canWrite());
            
            if (record != null) {
                // 如果数据库有记录，使用数据库中的信息
                fileInfo.setUploadTime(record.getCompleteTime());
            } else {
                // 如果数据库没有记录，使用文件的最后修改时间
                fileInfo.setUploadTime(Instant.ofEpochMilli(file.lastModified()).toString());
                log.info("通过路径查找文件，数据库无记录，使用文件系统信息 - 路径: {}, fileId: {}", relativePath, fileId);
            }
            
            return fileInfo;
            
        } catch (Exception e) {
            log.error("通过相对路径获取文件信息失败 - 路径: {}", relativePath, e);
            throw new FileTransferException("获取文件信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 分块下载文件
     * 支持HTTP Range请求，实现断点续传
     * 
     * @param fileId 文件ID
     * @param username 用户名
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     */
    public void downloadFileChunk(String fileId, String username, HttpServletRequest request, HttpServletResponse response) {
        // 使用带容错机制的查找方法
        FileTransferRecord record = findCompletedFileByMd5WithFallback(fileId, username);
        if (record == null) {
            throw new FileTransferException("文件不存在或无权限访问: " + fileId);
        }
        
        File file = new File(record.getFilePath());
        if (!file.exists()) {
            throw new FileTransferException("文件不存在: " + record.getFilePath());
        }
        
        try {
            // 获取用户配置
            UserConfig userConfig = authService.getUserConfig(username);
            
            long fileLength = file.length();
            long start = 0;
            long end = fileLength - 1;
            
            // 解析Range请求头
            String rangeHeader = request.getHeader("Range");
            if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
                String[] ranges = rangeHeader.substring("bytes=".length()).split("-");
                try {
                    if (ranges.length >= 1 && !ranges[0].isEmpty()) {
                        start = Long.parseLong(ranges[0]);
                    }
                    if (ranges.length >= 2 && !ranges[1].isEmpty()) {
                        end = Long.parseLong(ranges[1]);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的Range请求头: {}", rangeHeader);
                    // 忽略无效的Range，使用完整文件
                }
            }
            
            // 验证范围
            if (start > end || start >= fileLength) {
                response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
                response.setHeader("Content-Range", "bytes */" + fileLength);
                return;
            }
            
            // 调整end值
            if (end >= fileLength) {
                end = fileLength - 1;
            }
            
            long contentLength = end - start + 1;
            
            // 设置响应头
            if (rangeHeader != null) {
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
                response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + fileLength);
                response.setHeader("Accept-Ranges", "bytes");
            } else {
                response.setStatus(HttpServletResponse.SC_OK);
            }
            
            response.setContentType("application/octet-stream");
            response.setContentLengthLong(contentLength);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeFileName(record.getFileName()) + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            // 分块传输文件
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                // 跳转到起始位置
                if (start > 0) {
                    fis.skip(start);
                }
                
                byte[] buffer = new byte[8192];
                long totalRead = 0;
                long startTime = System.currentTimeMillis();
                
                while (totalRead < contentLength) {
                    int maxRead = (int) Math.min(buffer.length, contentLength - totalRead);
                    int bytesRead = fis.read(buffer, 0, maxRead);
                    
                    if (bytesRead == -1) {
                        break;
                    }
                    
                    os.write(buffer, 0, bytesRead);
                    totalRead += bytesRead;
                    
                    // 应用下载限速
                    if (userConfig.getRateLimitEnabled() && userConfig.getDownloadRateLimit() > 0) {
                        RateLimitUtils.applyRateLimit(username + "_download", userConfig.getDownloadRateLimit(), bytesRead);
                    }
                    
                    // 每传输1MB输出一次日志
                    if (totalRead % (1024 * 1024) == 0) {
                        log.debug("分块下载进度 - 用户: {}, 文件: {}, 已传输: {}/{}", 
                                username, record.getFileName(), totalRead, contentLength);
                    }
                }
                
                os.flush();
                
                long duration = System.currentTimeMillis() - startTime;
                double speed = duration > 0 ? (double) totalRead / duration * 1000 : 0; // bytes/s
                
                log.info("分块下载完成 - 用户: {}, 文件: {}, 范围: {}-{}, 大小: {}, 耗时: {}ms, 速度: {}/s", 
                        username, record.getFileName(), start, end, totalRead, duration, FileUtils.formatFileSize((long) speed));
            }
            
        } catch (IOException e) {
            log.error("分块下载文件失败 - 文件ID: {}", fileId, e);
            throw new FileTransferException("分块下载文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证相对路径的安全性
     * 防止路径遍历攻击（如使用..访问上级目录）
     * 
     * @param relativePath 相对路径
     * @return 是否安全
     */
    private boolean validateRelativePath(String relativePath) {
        if (relativePath == null || relativePath.trim().isEmpty()) {
            return false;
        }
        
        // 标准化路径
        Path path = Paths.get(relativePath).normalize();
        String normalizedPath = path.toString();
        
        // 检查是否包含危险操作
        if (normalizedPath.contains("..") || 
            normalizedPath.startsWith("/") || 
            normalizedPath.contains("\\..") ||
            normalizedPath.startsWith("\\")) {
            log.warn("检测到危险路径: {}", relativePath);
            return false;
        }
        
        // 检查路径是否为绝对路径
        if (path.isAbsolute()) {
            log.warn("不允许绝对路径: {}", relativePath);
            return false;
        }
        
        return true;
    }
    
    /**
     * 计算相对路径
     * 
     * @param absolutePath 绝对路径
     * @param basePath 基础路径
     * @return 相对路径
     */
    private String calculateRelativePath(String absolutePath, String basePath) {
        try {
            Path absolute = Paths.get(absolutePath);
            Path base = Paths.get(basePath);
            Path relative = base.relativize(absolute);
            return relative.toString().replace("\\", "/"); // 统一使用正斜杠
        } catch (Exception e) {
            log.warn("计算相对路径失败 - 绝对路径: {}, 基础路径: {}", absolutePath, basePath, e);
            return absolutePath; // 返回原路径
        }
    }
    
    /**
     * 检测文件类型
     * 
     * @param fileName 文件名
     * @return 文件类型
     */
    private String detectFileType(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown";
        }
        
        String extension = "";
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        // 常见文件类型映射
        switch (extension) {
            case "txt":
            case "md":
            case "log":
                return "text";
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "image";
            case "mp4":
            case "avi":
            case "mkv":
            case "mov":
            case "wmv":
                return "video";
            case "mp3":
            case "wav":
            case "flac":
            case "aac":
                return "audio";
            case "pdf":
                return "pdf";
            case "doc":
            case "docx":
                return "document";
            case "xls":
            case "xlsx":
                return "spreadsheet";
            case "ppt":
            case "pptx":
                return "presentation";
            case "zip":
            case "rar":
            case "7z":
            case "tar":
            case "gz":
                return "archive";
            case "java":
            case "js":
            case "py":
            case "cpp":
            case "c":
            case "php":
                return "code";
            default:
                return "unknown";
        }
    }

    /**
     * 构建文件上传完成响应
     */
    private FileUploadCompleteResponse buildCompleteResponse(FileTransferRecord record, String transferId, String username, boolean fastUpload) {
        FileUploadCompleteResponse response = new FileUploadCompleteResponse();
        response.setTransferId(transferId);
        response.setFileId(record.getFileId());
        response.setFileName(record.getFileName());
        response.setFileSize(record.getFileSize());
        response.setCompleteTime(record.getCompleteTime());
        response.setFastUpload(fastUpload);

        // 从extInfo中提取原始MD5
        String originalMd5 = extractOriginalMd5FromExtInfo(record.getExtInfo());
        response.setFileMd5(originalMd5);

        // 计算相对路径
        String basePath = properties.getDefaultConfig().getStoragePath();
        String relativePath = calculateRelativePath(record.getFilePath(), basePath);
        response.setRelativePath(relativePath);

        // 计算传输耗时
        if (record.getCreateTime() != null && record.getCompleteTime() != null) {
            try {
                Instant createTime = Instant.parse(record.getCreateTime());
                Instant completeTime = Instant.parse(record.getCompleteTime());
                long duration = completeTime.toEpochMilli() - createTime.toEpochMilli();
                response.setTransferDuration(duration);
            } catch (Exception e) {
                log.warn("计算传输耗时失败: {}", e.getMessage());
            }
        }

        return response;
    }

    /**
     * 从扩展信息中提取原始MD5值
     */
    private String extractOriginalMd5FromExtInfo(String extInfo) {
        if (extInfo == null || extInfo.trim().isEmpty()) {
            return null;
        }

        try {
            // 简单的JSON解析，提取originalMd5字段
            if (extInfo.contains("\"originalMd5\"")) {
                int startIndex = extInfo.indexOf("\"originalMd5\":\"") + "\"originalMd5\":\"".length();
                int endIndex = extInfo.indexOf("\"", startIndex);
                if (startIndex > 0 && endIndex > startIndex) {
                    return extInfo.substring(startIndex, endIndex);
                }
            }
        } catch (Exception e) {
            log.warn("解析extInfo失败: {}", extInfo, e);
        }

        return null;
    }


}