package com.sdesrd.filetransfer.server.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件权限管理工具类
 * 提供跨平台的文件权限设置功能，支持Windows和Linux/Unix系统
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class FilePermissionUtils {
    
    /**
     * 操作系统类型
     */
    private static final String OS_NAME = System.getProperty("os.name").toLowerCase();
    private static final boolean IS_WINDOWS = OS_NAME.contains("windows");
    private static final boolean IS_POSIX = !IS_WINDOWS;
    
    /**
     * 权限常量定义
     */
    public static final class Permissions {
        /** 只读权限（用户可读，其他无权限） */
        public static final String READ_ONLY = "r--------";
        /** 读写权限（用户可读写，其他无权限） */
        public static final String READ_WRITE = "rw-------";
        /** 完全权限（用户可读写执行，其他无权限） */
        public static final String FULL_USER = "rwx------";
        /** 目录默认权限（用户可读写执行，组和其他用户可读执行） */
        public static final String DIR_DEFAULT = "rwxr-xr-x";
    }
    
    /**
     * 设置文件为只读权限
     * 
     * @param filePath 文件路径
     * @return 是否设置成功
     */
    public static boolean setReadOnly(String filePath) {
        return setReadOnly(new File(filePath));
    }
    
    /**
     * 设置文件为只读权限
     * 
     * @param file 文件对象
     * @return 是否设置成功
     */
    public static boolean setReadOnly(File file) {
        if (file == null || !file.exists()) {
            log.warn("文件不存在，无法设置只读权限: {}", file);
            return false;
        }
        
        try {
            if (IS_WINDOWS) {
                return setReadOnlyWindows(file);
            } else {
                return setReadOnlyPosix(file);
            }
        } catch (Exception e) {
            log.error("设置文件只读权限失败: {}", file.getAbsolutePath(), e);
            return false;
        }
    }
    
    /**
     * 设置文件为可写权限
     * 
     * @param filePath 文件路径
     * @return 是否设置成功
     */
    public static boolean setWritable(String filePath) {
        return setWritable(new File(filePath));
    }
    
    /**
     * 设置文件为可写权限
     * 
     * @param file 文件对象
     * @return 是否设置成功
     */
    public static boolean setWritable(File file) {
        if (file == null || !file.exists()) {
            log.warn("文件不存在，无法设置可写权限: {}", file);
            return false;
        }
        
        try {
            if (IS_WINDOWS) {
                return setWritableWindows(file);
            } else {
                return setWritablePosix(file);
            }
        } catch (Exception e) {
            log.error("设置文件可写权限失败: {}", file.getAbsolutePath(), e);
            return false;
        }
    }
    
    /**
     * 设置目录权限（递归设置）
     * 
     * @param dirPath 目录路径
     * @param readOnly 是否设置为只读
     * @return 是否设置成功
     */
    public static boolean setDirectoryPermissions(String dirPath, boolean readOnly) {
        return setDirectoryPermissions(new File(dirPath), readOnly);
    }
    
    /**
     * 设置目录权限（递归设置）
     * 
     * @param dir 目录对象
     * @param readOnly 是否设置为只读
     * @return 是否设置成功
     */
    public static boolean setDirectoryPermissions(File dir, boolean readOnly) {
        if (dir == null || !dir.exists() || !dir.isDirectory()) {
            log.warn("目录不存在或不是目录，无法设置权限: {}", dir);
            return false;
        }
        
        try {
            // 设置目录本身的权限
            boolean success = readOnly ? setReadOnly(dir) : setWritable(dir);
            
            // 递归设置子文件和子目录的权限
            File[] children = dir.listFiles();
            if (children != null) {
                for (File child : children) {
                    if (child.isDirectory()) {
                        success &= setDirectoryPermissions(child, readOnly);
                    } else {
                        success &= readOnly ? setReadOnly(child) : setWritable(child);
                    }
                }
            }
            
            log.debug("设置目录权限完成: {} (只读: {})", dir.getAbsolutePath(), readOnly);
            return success;
            
        } catch (Exception e) {
            log.error("设置目录权限失败: {}", dir.getAbsolutePath(), e);
            return false;
        }
    }
    
    /**
     * 检查文件是否为只读
     * 
     * @param filePath 文件路径
     * @return 是否为只读
     */
    public static boolean isReadOnly(String filePath) {
        return isReadOnly(new File(filePath));
    }
    
    /**
     * 检查文件是否为只读
     * 
     * @param file 文件对象
     * @return 是否为只读
     */
    public static boolean isReadOnly(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        
        return file.canRead() && !file.canWrite();
    }
    
    /**
     * 获取文件权限信息
     * 
     * @param filePath 文件路径
     * @return 权限信息字符串
     */
    public static String getPermissionInfo(String filePath) {
        return getPermissionInfo(new File(filePath));
    }
    
    /**
     * 获取文件权限信息
     * 
     * @param file 文件对象
     * @return 权限信息字符串
     */
    public static String getPermissionInfo(File file) {
        if (file == null || !file.exists()) {
            return "文件不存在";
        }
        
        StringBuilder info = new StringBuilder();
        info.append("文件: ").append(file.getAbsolutePath()).append("\n");
        info.append("可读: ").append(file.canRead()).append("\n");
        info.append("可写: ").append(file.canWrite()).append("\n");
        info.append("可执行: ").append(file.canExecute()).append("\n");
        
        if (IS_POSIX) {
            try {
                Path path = file.toPath();
                Set<PosixFilePermission> permissions = Files.getPosixFilePermissions(path);
                info.append("POSIX权限: ").append(PosixFilePermissions.toString(permissions));
            } catch (Exception e) {
                info.append("POSIX权限: 获取失败 - ").append(e.getMessage());
            }
        } else {
            info.append("Windows权限: 基于File API");
        }
        
        return info.toString();
    }
    
    /**
     * Windows系统设置只读权限
     */
    private static boolean setReadOnlyWindows(File file) {
        try {
            // Windows使用File API设置只读
            boolean success = file.setReadOnly();
            log.debug("Windows设置只读权限: {} -> {}", file.getAbsolutePath(), success);
            return success;
        } catch (Exception e) {
            log.error("Windows设置只读权限失败: {}", file.getAbsolutePath(), e);
            return false;
        }
    }
    
    /**
     * Windows系统设置可写权限
     */
    private static boolean setWritableWindows(File file) {
        try {
            // Windows使用File API设置可写
            boolean success = file.setWritable(true);
            log.debug("Windows设置可写权限: {} -> {}", file.getAbsolutePath(), success);
            return success;
        } catch (Exception e) {
            log.error("Windows设置可写权限失败: {}", file.getAbsolutePath(), e);
            return false;
        }
    }
    
    /**
     * POSIX系统设置只读权限
     */
    private static boolean setReadOnlyPosix(File file) throws IOException {
        Path path = file.toPath();
        Set<PosixFilePermission> permissions = PosixFilePermissions.fromString(Permissions.READ_ONLY);
        Files.setPosixFilePermissions(path, permissions);
        log.debug("POSIX设置只读权限: {}", file.getAbsolutePath());
        return true;
    }
    
    /**
     * POSIX系统设置可写权限
     */
    private static boolean setWritablePosix(File file) throws IOException {
        Path path = file.toPath();
        Set<PosixFilePermission> permissions = PosixFilePermissions.fromString(Permissions.READ_WRITE);
        Files.setPosixFilePermissions(path, permissions);
        log.debug("POSIX设置可写权限: {}", file.getAbsolutePath());
        return true;
    }
    
    /**
     * 临时权限管理器
     * 用于在操作期间临时修改权限，操作完成后自动恢复
     */
    public static class TemporaryPermissionManager implements AutoCloseable {
        private final File file;
        private final boolean originalReadOnly;
        
        public TemporaryPermissionManager(File file) {
            this.file = file;
            this.originalReadOnly = isReadOnly(file);
        }
        
        /**
         * 临时设置为可写权限
         */
        public boolean makeWritable() {
            if (!originalReadOnly) {
                return true; // 原本就是可写的
            }
            return setWritable(file);
        }
        
        @Override
        public void close() {
            // 恢复原始权限
            if (originalReadOnly) {
                setReadOnly(file);
                log.debug("恢复文件只读权限: {}", file.getAbsolutePath());
            }
        }
    }
}
