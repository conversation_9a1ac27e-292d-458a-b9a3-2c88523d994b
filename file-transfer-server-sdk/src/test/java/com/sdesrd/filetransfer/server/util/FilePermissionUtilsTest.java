package com.sdesrd.filetransfer.server.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.io.TempDir;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件权限管理工具类测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("文件权限管理工具类测试")
class FilePermissionUtilsTest {
    
    @TempDir
    Path tempDir;
    
    private File testFile;
    private File testDir;
    
    @BeforeEach
    void setUp() throws IOException {
        // 创建测试文件
        testFile = tempDir.resolve("test-file.txt").toFile();
        Files.write(testFile.toPath(), "测试文件内容".getBytes());
        
        // 创建测试目录
        testDir = tempDir.resolve("test-dir").toFile();
        testDir.mkdirs();
        
        // 在测试目录中创建子文件
        File subFile = new File(testDir, "sub-file.txt");
        Files.write(subFile.toPath(), "子文件内容".getBytes());
    }
    
    @AfterEach
    void tearDown() {
        // 清理：确保文件可写，以便删除
        if (testFile != null && testFile.exists()) {
            FilePermissionUtils.setWritable(testFile);
        }
        if (testDir != null && testDir.exists()) {
            FilePermissionUtils.setDirectoryPermissions(testDir, false);
        }
    }
    
    @Test
    @DisplayName("设置文件为只读权限测试")
    void testSetReadOnly() {
        // 确保文件初始状态为可写
        assertTrue(testFile.canWrite(), "测试文件初始应为可写");
        
        // 设置为只读
        boolean result = FilePermissionUtils.setReadOnly(testFile);
        assertTrue(result, "设置只读权限应成功");
        
        // 验证文件变为只读
        assertTrue(testFile.canRead(), "文件应仍可读");
        assertFalse(testFile.canWrite(), "文件应变为不可写");
        assertTrue(FilePermissionUtils.isReadOnly(testFile), "文件应被识别为只读");
    }
    
    @Test
    @DisplayName("设置文件为可写权限测试")
    void testSetWritable() {
        // 先设置为只读
        FilePermissionUtils.setReadOnly(testFile);
        assertFalse(testFile.canWrite(), "文件应为只读状态");
        
        // 设置为可写
        boolean result = FilePermissionUtils.setWritable(testFile);
        assertTrue(result, "设置可写权限应成功");
        
        // 验证文件变为可写
        assertTrue(testFile.canRead(), "文件应仍可读");
        assertTrue(testFile.canWrite(), "文件应变为可写");
        assertFalse(FilePermissionUtils.isReadOnly(testFile), "文件不应被识别为只读");
    }
    
    @Test
    @DisplayName("设置目录权限测试")
    void testSetDirectoryPermissions() throws IOException {
        // 确保子文件存在
        File subFile = new File(testDir, "sub-file.txt");
        if (!subFile.exists()) {
            Files.write(subFile.toPath(), "子文件内容".getBytes());
        }

        // 确保目录和子文件初始状态为可写
        assertTrue(testDir.canWrite(), "测试目录初始应为可写");
        assertTrue(subFile.canWrite(), "子文件初始应为可写");

        // 设置目录为只读（递归）
        boolean result = FilePermissionUtils.setDirectoryPermissions(testDir, true);
        assertTrue(result, "设置目录只读权限应成功");

        // 验证目录和子文件都变为只读
        assertTrue(testDir.canRead(), "目录应仍可读");
        assertFalse(testDir.canWrite(), "目录应变为不可写");
        assertTrue(subFile.canRead(), "子文件应仍可读");
        assertFalse(subFile.canWrite(), "子文件应变为不可写");

        // 恢复为可写
        result = FilePermissionUtils.setDirectoryPermissions(testDir, false);
        assertTrue(result, "恢复目录可写权限应成功");

        // 验证目录和子文件都变为可写
        assertTrue(testDir.canWrite(), "目录应变为可写");
        assertTrue(subFile.canWrite(), "子文件应变为可写");
    }
    
    @Test
    @DisplayName("检查文件是否为只读测试")
    void testIsReadOnly() {
        // 初始状态应为可写
        assertFalse(FilePermissionUtils.isReadOnly(testFile), "初始文件不应为只读");
        
        // 设置为只读后检查
        FilePermissionUtils.setReadOnly(testFile);
        assertTrue(FilePermissionUtils.isReadOnly(testFile), "设置后文件应为只读");
        
        // 恢复可写后检查
        FilePermissionUtils.setWritable(testFile);
        assertFalse(FilePermissionUtils.isReadOnly(testFile), "恢复后文件不应为只读");
    }
    
    @Test
    @DisplayName("获取文件权限信息测试")
    void testGetPermissionInfo() {
        // 获取权限信息
        String info = FilePermissionUtils.getPermissionInfo(testFile);
        
        // 验证信息包含必要内容
        assertNotNull(info, "权限信息不应为null");
        assertTrue(info.contains(testFile.getAbsolutePath()), "权限信息应包含文件路径");
        assertTrue(info.contains("可读"), "权限信息应包含可读状态");
        assertTrue(info.contains("可写"), "权限信息应包含可写状态");
        assertTrue(info.contains("可执行"), "权限信息应包含可执行状态");
    }
    
    @Test
    @DisplayName("临时权限管理器测试")
    void testTemporaryPermissionManager() throws Exception {
        // 先设置文件为只读
        FilePermissionUtils.setReadOnly(testFile);
        assertTrue(FilePermissionUtils.isReadOnly(testFile), "文件应为只读");
        
        // 使用临时权限管理器
        try (FilePermissionUtils.TemporaryPermissionManager manager = 
                new FilePermissionUtils.TemporaryPermissionManager(testFile)) {
            
            // 临时设置为可写
            boolean result = manager.makeWritable();
            assertTrue(result, "临时设置可写应成功");
            assertTrue(testFile.canWrite(), "文件应临时变为可写");
            
            // 在这里可以进行需要写权限的操作
            
        } // 自动恢复权限
        
        // 验证权限已恢复为只读
        assertTrue(FilePermissionUtils.isReadOnly(testFile), "文件权限应自动恢复为只读");
    }
    
    @Test
    @DisplayName("处理不存在文件的测试")
    void testHandleNonExistentFile() {
        File nonExistentFile = new File(tempDir.toFile(), "non-existent.txt");
        
        // 测试设置不存在文件的权限
        assertFalse(FilePermissionUtils.setReadOnly(nonExistentFile), "设置不存在文件权限应失败");
        assertFalse(FilePermissionUtils.setWritable(nonExistentFile), "设置不存在文件权限应失败");
        assertFalse(FilePermissionUtils.isReadOnly(nonExistentFile), "不存在文件不应为只读");
        
        // 测试获取不存在文件的权限信息
        String info = FilePermissionUtils.getPermissionInfo(nonExistentFile);
        assertTrue(info.contains("文件不存在"), "权限信息应指示文件不存在");
    }
    
    @Test
    @DisplayName("处理null参数的测试")
    void testHandleNullParameters() {
        // 测试null文件对象
        assertFalse(FilePermissionUtils.setReadOnly((File) null), "null文件设置权限应失败");
        assertFalse(FilePermissionUtils.setWritable((File) null), "null文件设置权限应失败");
        assertFalse(FilePermissionUtils.isReadOnly((File) null), "null文件不应为只读");
        
        // 测试null路径字符串
        assertFalse(FilePermissionUtils.setReadOnly((String) null), "null路径设置权限应失败");
        assertFalse(FilePermissionUtils.setWritable((String) null), "null路径设置权限应失败");
        assertFalse(FilePermissionUtils.isReadOnly((String) null), "null路径不应为只读");
        
        // 测试获取null文件的权限信息
        String info = FilePermissionUtils.getPermissionInfo((File) null);
        assertTrue(info.contains("文件不存在"), "null文件权限信息应指示文件不存在");
    }
    
    @Test
    @DisplayName("跨平台兼容性测试")
    void testCrossPlatformCompatibility() {
        // 这个测试主要验证在不同操作系统上权限设置不会抛出异常
        
        // 测试文件权限设置
        assertDoesNotThrow(() -> {
            FilePermissionUtils.setReadOnly(testFile);
            FilePermissionUtils.setWritable(testFile);
        }, "跨平台权限设置不应抛出异常");
        
        // 测试目录权限设置
        assertDoesNotThrow(() -> {
            FilePermissionUtils.setDirectoryPermissions(testDir, true);
            FilePermissionUtils.setDirectoryPermissions(testDir, false);
        }, "跨平台目录权限设置不应抛出异常");
        
        // 测试权限信息获取
        assertDoesNotThrow(() -> {
            String info = FilePermissionUtils.getPermissionInfo(testFile);
            assertNotNull(info, "权限信息不应为null");
        }, "跨平台权限信息获取不应抛出异常");
    }
    
    @Test
    @DisplayName("权限常量定义测试")
    void testPermissionConstants() {
        // 验证权限常量定义正确
        assertNotNull(FilePermissionUtils.Permissions.READ_ONLY, "只读权限常量不应为null");
        assertNotNull(FilePermissionUtils.Permissions.READ_WRITE, "读写权限常量不应为null");
        assertNotNull(FilePermissionUtils.Permissions.FULL_USER, "完全权限常量不应为null");
        assertNotNull(FilePermissionUtils.Permissions.DIR_DEFAULT, "目录默认权限常量不应为null");
        
        // 验证权限常量格式
        assertEquals(9, FilePermissionUtils.Permissions.READ_ONLY.length(), "只读权限常量长度应为9");
        assertEquals(9, FilePermissionUtils.Permissions.READ_WRITE.length(), "读写权限常量长度应为9");
        assertEquals(9, FilePermissionUtils.Permissions.FULL_USER.length(), "完全权限常量长度应为9");
        assertEquals(9, FilePermissionUtils.Permissions.DIR_DEFAULT.length(), "目录默认权限常量长度应为9");
    }
}
