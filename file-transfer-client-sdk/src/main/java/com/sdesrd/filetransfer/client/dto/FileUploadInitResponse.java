package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 文件上传初始化响应
 */
@Data
public class FileUploadInitResponse {
    
    /**
     * 传输ID
     */
    private String transferId;
    
    /**
     * 文件ID
     */
    private String fileId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 是否秒传
     */
    private Boolean fastUpload;
    
    /**
     * 分块大小
     */
    private Long chunkSize;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
} 